defaults:
  - base
  - evaluate_api_llm

output_path: results/spatial-0724/active_3_objects/test/rot_cognitive_map.json

eval_model_type: api # api or vllm
model_path: Qwen/Qwen2.5-3B-Instruct
api_model_info:
  model_name:  gemini-2.5-pro
  max_concurrency: 64

actor_rollout_ref:
  rollout:
    max_model_len: 10000
    gpu_memory_utilization: 0.8

agent_proxy:
  max_turn: 15 # should be same as max_actions_per_traj
  action_sep: "||"
  max_actions_per_turn: 1 # how many actions can be output at most in a single turn

es_manager:
  val:
    env_groups: 1 # 450
    group_size: 1 # should be set to 1 because when val temperature is set to 0 and group size > 1, there will be repetitive prompts which leads to same trajectory.
    env_configs:
      # tags: ["PassiveRotDual"]
      # n_groups: [1]
      # tags: ["PassiveRot", "PassiveRotDual", "PassiveCircularRot", "PassivePoV", "PassiveDir", "PassiveE2A", "PassiveLoc", "PassiveFalseBeliefRotation", "PassiveFalseBeliefMovement"]
      # n_groups: [50, 50, 50, 50, 50, 50, 50, 50, 50]
      # tags: ["PassiveRot", "PassiveRotDual", "PassiveCircularRot", "PassivePoV", "PassiveDir", "PassiveE2A", "PassiveLoc", "PassiveFalseBeliefRotation", "PassiveFalseBeliefMovement"]
      # n_groups: [50, 50, 50, 50, 50, 50, 50, 50, 50]
      # tags: ["PassiveTopDownRot", "PassiveTopDownCounterRot", "PassiveTopDownRotDual", "PassiveTopDownCircularRot", "PassiveTopDownPoV", "PassiveTopDownAllPairs", "PassiveTopDownE2A", "PassiveTopDownLoc", "PassiveTopDownFalseBelief", "PassiveTopDownFalseBeliefMovement"]
      # n_groups: [50, 50, 50, 50, 50, 50, 50, 50, 50, 50]
      tags: ["ActiveRot"]
      n_groups: [1] # TODO: If not set, all env names divide nums equally. Under the same group, the env config and env seed (prompt) are equal in each generation

